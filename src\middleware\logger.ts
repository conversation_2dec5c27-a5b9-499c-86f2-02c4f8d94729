import { Request, Response, NextFunction } from 'express';
import morgan from 'morgan';

/**
 * Custom token for morgan to log response time in a more readable format
 */
morgan.token('response-time-ms', (_req: Request, res: Response) => {
  const responseTime = res.getHeader('X-Response-Time');
  return responseTime ? `${responseTime}ms` : '-';
});

/**
 * Custom token for morgan to log request ID
 */
morgan.token('request-id', (req: Request) => {
  return (req as any).requestId || '-';
});



/**
 * Development logging format
 */
const developmentFormat = ':method :url :status :res[content-length] - :response-time ms';

/**
 * Production logging format
 */
const productionFormat = ':remote-addr [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms';

/**
 * Get morgan logger based on environment
 */
export const getLogger = () => {
  const format = process.env['NODE_ENV'] === 'production' ? productionFormat : developmentFormat;

  return morgan(format, {
    skip: (req: Request, _res: Response) => {
      // Skip logging for health check endpoints in production
      if (process.env['NODE_ENV'] === 'production' && req.url === '/api/health') {
        return true;
      }
      return false;
    },
    stream: {
      write: (message: string) => {
        // Remove trailing newline and log
        console.log(message.trim());
      }
    }
  });
};

/**
 * Request ID middleware - adds unique ID to each request
 */
export const requestId = (req: Request, res: Response, next: NextFunction): void => {
  const id = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  (req as any).requestId = id;
  res.setHeader('X-Request-ID', id);
  next();
};

/**
 * Response time middleware
 */
export const responseTime = (_req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();

  // Use on-headers to set the response time header just before headers are sent
  res.on('finish', () => {
    const duration = Date.now() - start;
    // This won't cause an error since we're not actually setting a header
    // Just logging the response time
    console.log(`Response time: ${duration}ms`);
  });

  next();
};

/**
 * Request logging middleware for detailed logging
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  // Log request details in development
  if (process.env['NODE_ENV'] === 'development') {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`, {
      headers: req.headers,
      query: req.query,
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // Log response details in development
    if (process.env['NODE_ENV'] === 'development') {
      console.log(`[${new Date().toISOString()}] Response ${res.statusCode} - ${duration}ms`);
    }
  });
  
  next();
};
