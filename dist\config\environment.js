"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTest = exports.isProduction = exports.isDevelopment = exports.validateConfig = exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
exports.config = {
    port: parseInt(process.env['PORT'] || '3000', 10),
    host: process.env['HOST'] || 'localhost',
    nodeEnv: process.env['NODE_ENV'] || 'development',
    apiPrefix: process.env['API_PREFIX'] || '/api',
    apiVersion: process.env['API_VERSION'] || 'v1',
    corsOrigins: process.env['CORS_ORIGINS']?.split(',') || ['http://localhost:3000'],
    rateLimitWindow: parseInt(process.env['RATE_LIMIT_WINDOW'] || '900000', 10),
    rateLimitMax: parseInt(process.env['RATE_LIMIT_MAX'] || '100', 10),
    database: {
        url: process.env['DATABASE_URL'] || '',
        host: process.env['DB_HOST'] || 'localhost',
        port: parseInt(process.env['DB_PORT'] || '5432', 10),
        name: process.env['DB_NAME'] || 'cravin_concierge',
        username: process.env['DB_USERNAME'] || '',
        password: process.env['DB_PASSWORD'] || '',
    },
    logging: {
        level: process.env['LOG_LEVEL'] || 'info',
        format: process.env['LOG_FORMAT'] || 'combined',
    },
    app: {
        name: process.env['APP_NAME'] || 'Cravin Concierge API',
        version: process.env['APP_VERSION'] || '1.0.0',
        description: process.env['APP_DESCRIPTION'] || 'TypeScript Express.js MVC API',
    }
};
const validateConfig = () => {
    const requiredVars = [];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
        throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
};
exports.validateConfig = validateConfig;
const isDevelopment = () => {
    return exports.config.nodeEnv === 'development';
};
exports.isDevelopment = isDevelopment;
const isProduction = () => {
    return exports.config.nodeEnv === 'production';
};
exports.isProduction = isProduction;
const isTest = () => {
    return exports.config.nodeEnv === 'test';
};
exports.isTest = isTest;
//# sourceMappingURL=environment.js.map