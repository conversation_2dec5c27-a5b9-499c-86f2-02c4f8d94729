{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AACA,4EAA2C;AAK9B,QAAA,UAAU,GAAG,IAAA,4BAAS,EAAC;IAClC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,GAAG;IACR,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,2CAA2C;QACpD,KAAK,EAAE,qBAAqB;QAC5B,UAAU,EAAE,GAAG;QACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;CACF,CAAC,CAAC;AAKU,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,CAAC,MAA0B,EAAE,QAAsD,EAAE,EAAE;QAE7F,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrB,OAAO;QACT,CAAC;QAGD,MAAM,cAAc,GAAG;YACrB,uBAAuB;YACvB,uBAAuB;SAExB,CAAC;QAEF,IAAI,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,EAAE,CAAC;YACvF,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7D,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,CAAC;IACrE,cAAc,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC;IACnD,WAAW,EAAE,IAAI;IACjB,MAAM,EAAE,KAAK;CACd,CAAC;AAKK,MAAM,eAAe,GAAG,CAAC,IAAa,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAExF,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IAGjC,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACzC,GAAG,CAAC,SAAS,CAAC,2BAA2B,EAAE,qCAAqC,CAAC,CAAC;IAClF,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,uDAAuD,CAAC,CAAC;IACxF,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACpC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAE9B,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,eAAe,mBAc1B;AAKK,MAAM,qBAAqB,GAAG,CAAC,IAAa,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE;;;;;;;;;;;;GAYxC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAE/B,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAhBW,QAAA,qBAAqB,yBAgBhC"}