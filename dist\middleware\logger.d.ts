import { Request, Response, NextFunction } from 'express';
export declare const getLogger: () => (req: Request<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>, res: Response<any, Record<string, any>>, callback: (err?: Error) => void) => void;
export declare const requestId: (req: Request, res: Response, next: NextFunction) => void;
export declare const responseTime: (_req: Request, res: Response, next: NextFunction) => void;
export declare const requestLogger: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=logger.d.ts.map