export declare const config: {
    port: number;
    host: string;
    nodeEnv: string;
    apiPrefix: string;
    apiVersion: string;
    corsOrigins: string[];
    rateLimitWindow: number;
    rateLimitMax: number;
    database: {
        url: string;
        host: string;
        port: number;
        name: string;
        username: string;
        password: string;
    };
    logging: {
        level: string;
        format: string;
    };
    app: {
        name: string;
        version: string;
        description: string;
    };
};
export declare const validateConfig: () => void;
export declare const isDevelopment: () => boolean;
export declare const isProduction: () => boolean;
export declare const isTest: () => boolean;
//# sourceMappingURL=environment.d.ts.map